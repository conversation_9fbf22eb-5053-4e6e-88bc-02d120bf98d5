FROM oven/bun:1

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y openssl && rm -rf /var/lib/apt/lists/*

# Copy package files first for better caching
COPY package.json bun.lock* tsconfig.json ./
COPY shared/package.json ./shared/
COPY server/package.json ./server/
COPY client/package.json ./client/

# Copy source code (needed for postinstall scripts)
COPY shared ./shared
COPY server ./server

# Install dependencies (this layer will be cached if package files don't change)
ENV PRISMA_GENERATE_SKIP_DOWNLOAD=true
RUN bun install --frozen-lockfile

# Build shared workspace
RUN cd shared && bun run build

# Generate Prisma client with optimizations
RUN cd server && PRISMA_GENERATE_SKIP_DOWNLOAD=true bunx prisma generate

# Build server
RUN cd server && bun run build

# Copy and set permissions for entrypoint script
COPY server/docker-entrypoint /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint

# Expose port
EXPOSE 8080

# Set working directory to server
WORKDIR /app/server

# Use entrypoint script
ENTRYPOINT ["docker-entrypoint"]
CMD ["bun", "run", "start"]
