FROM oven/bun:1

ENV PRISMA_GENERATE_SKIP_DOWNLOAD=true
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y openssl && rm -rf /var/lib/apt/lists/*

# 1. Copy only lock & package files for dependency install
COPY bun.lock* tsconfig.json ./
COPY shared/package.json ./shared/
COPY server/package.json ./server/

# Create minimal package.json for server build (without client workspace)
RUN echo '{"name":"sumopod-server-build","workspaces":["./server","./shared"],"scripts":{"build:shared":"cd shared && bun run build","build:server":"cd server && bun run build"}}' > package.json

# 2. Install dependencies (cached unless deps change)
RUN bun install --frozen-lockfile

# 3. Now copy actual code after deps are cached
COPY shared ./shared
COPY server ./server

# 4. Build shared lib
RUN cd shared && bun run build

# 5. Generate Prisma Client
RUN cd server && bunx prisma generate

# 6. Build server
RUN cd server && bun run build

# 7. Setup entrypoint
COPY server/docker-entrypoint /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint

EXPOSE 8080
WORKDIR /app/server
ENTRYPOINT ["docker-entrypoint"]
CMD ["bun", "run", "start"]
